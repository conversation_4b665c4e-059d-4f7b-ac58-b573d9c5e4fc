# IBKR Portfolio Tracker

A beautiful, real-time performance dashboard for Interactive Brokers portfolios built with Next.js, TypeScript, and Tailwind CSS. The dashboard displays portfolio returns across multiple time periods with elegant visualizations and live data synchronization via Vercel KV.

## ✨ Features

- **Real-time Performance Tracking**: Monitor portfolio returns across 1D, 7D, MTD, 1M, YTD, and 1Y periods
- **Beautiful Visualizations**: Custom SVG charts with smooth animations and gradients
- **IBKR Connection Status**: Live connection indicator with automatic data sync
- **Responsive Design**: Optimized for desktop, tablet, and mobile devices
- **Dark Mode Support**: Automatic theme switching based on system preferences
- **Auto-refresh**: Data updates every 30 seconds automatically
- **Error Handling**: Graceful fallbacks and retry mechanisms
- **Smooth Animations**: Fade-in, slide-up, and pulse animations for enhanced UX

## 🚀 Tech Stack

- **Framework**: Next.js 15.4.4 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS 4.0
- **Data Storage**: Vercel KV (Redis)
- **Deployment**: Vercel
- **Charts**: Custom SVG-based visualizations

## 📊 Dashboard Components

### Performance Cards
- Six time period cards (1D, 7D, MTD, 1M, YTD, 1Y)
- Color-coded returns (green for positive, red for negative)
- Hover animations and responsive grid layout

### Portfolio Chart
- Real-time portfolio value visualization
- Gradient fills and animated data points
- Min/max values and statistics display
- Smooth path animations

### Connection Status
- Live IBKR connection indicator
- Last updated timestamp
- Auto-sync status with Vercel KV

## 🛠️ Getting Started

### Prerequisites
- Node.js 18+
- Yarn package manager
- Vercel account (for KV storage)

### Installation

1. Clone the repository:
```bash
git clone https://github.com/TunyatapVich/ibkr-vercel.git
cd ibkr-vercel
```

2. Install dependencies:
```bash
yarn install
```

3. Set up environment variables:
```bash
# Create .env.local file
KV_REST_API_URL=your_vercel_kv_url
KV_REST_API_TOKEN=your_vercel_kv_token
```

4. Run the development server:
```bash
yarn dev
```

5. Open [http://localhost:3000](http://localhost:3000) in your browser

## 📡 API Endpoints

### GET /api/ibkr-performance
Returns current portfolio performance data from Vercel KV or fallback data.

**Response Format:**
```json
{
  "lastUpdated": "2025-07-29T02:23:34Z",
  "summary": {
    "d1": 0.35,
    "d7": 1.28,
    "mtd": 2.45,
    "m1": 4.15,
    "ytd": 13.12,
    "y1": 23.20
  },
  "chartData": [
    { "date": "2025-07-27", "value": 141.80 },
    { "date": "2025-07-28", "value": 142.30 }
  ]
}
```

### POST /api/ibkr-performance
Updates portfolio performance data in Vercel KV storage.

**Request Body:** Same format as GET response

## 🎨 Customization

### Styling
- Modify `src/app/globals.css` for global styles and animations
- Update Tailwind classes in components for design changes
- Customize color schemes in component files

### Data Structure
- Extend the `Summary` type in components for additional time periods
- Modify the `PERIODS` array to add/remove time period cards
- Update chart data structure in `PerformanceChart.tsx`

## 📱 Responsive Design

The dashboard is fully responsive with breakpoints:
- **Mobile**: 2-column grid for performance cards
- **Tablet**: 3-column grid with optimized spacing
- **Desktop**: 6-column grid with full chart display

## 🔄 Data Flow

1. **Frontend**: React components fetch data from API every 30 seconds
2. **API**: Next.js API routes handle data retrieval from Vercel KV
3. **Storage**: Vercel KV stores real-time portfolio data
4. **External**: Separate service updates KV with IBKR data (to be implemented)

## 🚀 Deployment

### Deploy to Vercel

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Set up environment variables in Vercel dashboard
4. Deploy automatically on push

### Environment Variables
```bash
KV_REST_API_URL=your_vercel_kv_rest_api_url
KV_REST_API_TOKEN=your_vercel_kv_rest_api_token
```

## 🔮 Future Enhancements

- [ ] Add more chart types (candlestick, volume, etc.)
- [ ] Implement portfolio allocation breakdown
- [ ] Add historical performance comparison
- [ ] Include dividend tracking
- [ ] Add export functionality (PDF, CSV)
- [ ] Implement alerts and notifications
- [ ] Add multi-portfolio support

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Interactive Brokers for the trading platform
- Vercel for hosting and KV storage
- Next.js team for the amazing framework
- Tailwind CSS for the utility-first styling approach
