import { kv } from '@vercel/kv';
import { NextResponse } from 'next/server';

// Example data, in a real app you would update this from your backend or API
const data = {
  lastUpdated: '2025-07-29T02:23:34Z',
  summary: {
    d1: 0.35,
    d7: 1.28,
    mtd: 2.45,
    ytd: 13.12,
    y1: 23.20,
  },
  chartData: [
    { date: '2025-07-27', value: 141.80 },
    { date: '2025-07-28', value: 142.30 },
  ],
};

export async function GET() {
  // Get data from KV
  const value = await kv.get('ibkr-performance');
  let parsed = data;
  if (typeof value === 'string') {
    try {
      parsed = JSON.parse(value);
    } catch (e) {
      parsed = data;
    }
  }
  return NextResponse.json(parsed);
}

export async function POST(request: Request) {
  const body = await request.json();
  await kv.set('ibkr-performance', JSON.stringify(body));
  return NextResponse.json({ ok: true });
}
