import { kv } from '@vercel/kv';
import { NextResponse } from 'next/server';

// Fallback data in case KV is not available or empty
const fallbackData = {
  lastUpdated: '2025-07-29T02:23:34Z',
  summary: {
    d1: 0.35,
    d7: 1.28,
    mtd: 2.45,
    m1: 4.15,
    ytd: 13.12,
    y1: 23.20,
  },
  chartData: [
    { date: '2025-07-27', value: 141.80 },
    { date: '2025-07-28', value: 142.30 },
  ],
};

export async function GET() {
  try {
    // Get data from Vercel KV
    const kvData = await kv.get('ibkr-performance');

    // If we have data in KV, use it; otherwise use fallback
    if (kvData) {
      return NextResponse.json(kvData);
    } else {
      return NextResponse.json(fallbackData);
    }
  } catch (error) {
    console.error('Error fetching from KV:', error);
    // Return fallback data if KV fails
    return NextResponse.json(fallbackData);
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json();

    // Validate the data structure
    if (!body.lastUpdated || !body.summary || !body.chartData) {
      return NextResponse.json(
        { error: 'Invalid data structure' },
        { status: 400 }
      );
    }

    // Store data in Vercel KV
    await kv.set('ibkr-performance', body);

    return NextResponse.json({ success: true, message: 'Data updated successfully' });
  } catch (error) {
    console.error('Error updating KV:', error);
    return NextResponse.json(
      { error: 'Failed to update data' },
      { status: 500 }
    );
  }
}
