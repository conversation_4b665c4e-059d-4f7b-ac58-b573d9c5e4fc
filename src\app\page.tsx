"use client"
import PerformanceDashboard from "../components/PerformanceDashboard";

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-zinc-50 to-zinc-100 dark:from-zinc-950 dark:to-zinc-900 py-8 px-4">
      <div className="container mx-auto">
        {/* Header Section */}
        <div className="text-center mb-12 animate-fade-in">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-600 rounded-full mb-4 animate-pulse-glow">
            <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
            </svg>
          </div>
          <h1 className="text-5xl font-bold mb-3 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            IBKR Portfolio Tracker
          </h1>
          <p className="text-lg text-zinc-600 dark:text-zinc-400 max-w-2xl mx-auto">
            Real-time performance monitoring for your Interactive Brokers portfolio with beautiful visualizations and comprehensive analytics
          </p>
        </div>

        {/* Main Dashboard */}
        <PerformanceDashboard />

        {/* Footer Section */}
        <div className="text-center mt-12 animate-fade-in">
          <div className="inline-flex items-center space-x-2 text-sm text-zinc-500 dark:text-zinc-400 bg-white dark:bg-zinc-900 px-4 py-2 rounded-full border border-zinc-200 dark:border-zinc-700">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span>Live data sync via Vercel KV</span>
          </div>
          <p className="text-xs text-zinc-400 dark:text-zinc-500 mt-4">
            Built with Next.js, TypeScript, and Tailwind CSS
          </p>
        </div>
      </div>
    </div>
  );
}
