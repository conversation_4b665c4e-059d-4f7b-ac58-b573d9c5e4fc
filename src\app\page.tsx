"use client"
import { useEffect, useState } from "react";

type Summary = {
  d1: number;
  d7: number;
  mtd: number;
  m1: number;
  ytd: number;
  y1: number;
};

type PerformanceData = {
  lastUpdated: string;
  summary: Summary;
  chartData: { date: string; value: number }[];
};

const PERIODS = [
  { key: "d1", label: "1D" },
  { key: "d7", label: "7D" },
  { key: "mtd", label: "MTD" },
  { key: "m1", label: "1M" },
  { key: "ytd", label: "YTD" },
  { key: "y1", label: "1Y" },
];

function IBKRPerformanceDashboard() {
  const [data, setData] = useState<PerformanceData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await fetch("/api/ibkr-performance");
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        const result = await response.json();
        setData(result);
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch data');
        console.error('Error fetching performance data:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const formatPercentage = (value: number) => {
    const formatted = value.toFixed(2);
    const sign = value >= 0 ? '+' : '';
    return `${sign}${formatted}%`;
  };

  const getPerformanceColor = (value: number) => {
    if (value > 0) return 'text-green-600 dark:text-green-400';
    if (value < 0) return 'text-red-600 dark:text-red-400';
    return 'text-zinc-600 dark:text-zinc-400';
  };

  if (loading) {
    return (
      <div className="w-full max-w-4xl mx-auto bg-white dark:bg-zinc-900 rounded-lg shadow-lg p-6 mb-8">
        <div className="animate-pulse">
          <div className="h-6 bg-zinc-200 dark:bg-zinc-700 rounded w-48 mb-6"></div>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-6">
            {Array.from({ length: 6 }).map((_, i) => (
              <div key={i} className="text-center">
                <div className="h-4 bg-zinc-200 dark:bg-zinc-700 rounded w-8 mx-auto mb-2"></div>
                <div className="h-6 bg-zinc-200 dark:bg-zinc-700 rounded w-16 mx-auto"></div>
              </div>
            ))}
          </div>
          <div className="h-4 bg-zinc-200 dark:bg-zinc-700 rounded w-64"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full max-w-4xl mx-auto bg-white dark:bg-zinc-900 rounded-lg shadow-lg p-6 mb-8">
        <div className="text-center">
          <div className="text-red-600 dark:text-red-400 mb-2">⚠️ Error Loading Data</div>
          <div className="text-sm text-zinc-600 dark:text-zinc-400">{error}</div>
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="w-full max-w-4xl mx-auto bg-white dark:bg-zinc-900 rounded-lg shadow-lg p-6 mb-8">
        <div className="text-center text-zinc-600 dark:text-zinc-400">
          No performance data available
        </div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-4xl mx-auto bg-white dark:bg-zinc-900 rounded-lg shadow-lg p-6 mb-8 border border-zinc-200 dark:border-zinc-800">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-zinc-900 dark:text-zinc-100">
          IBKR Performance Dashboard
        </h2>
        <div className="text-xs text-zinc-500 dark:text-zinc-400">
          Last updated: {new Date(data.lastUpdated).toLocaleString()}
        </div>
      </div>

      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-6">
        {PERIODS.map((period) => {
          const value = data.summary[period.key as keyof Summary];
          return (
            <div
              key={period.key}
              className="bg-zinc-50 dark:bg-zinc-800 rounded-lg p-4 text-center border border-zinc-200 dark:border-zinc-700 hover:shadow-md transition-shadow"
            >
              <div className="text-xs font-medium text-zinc-500 dark:text-zinc-400 mb-2 uppercase tracking-wide">
                {period.label}
              </div>
              <div className={`text-xl font-bold ${getPerformanceColor(value)}`}>
                {formatPercentage(value)}
              </div>
            </div>
          );
        })}
      </div>

      <div className="border-t border-zinc-200 dark:border-zinc-700 pt-4">
        <div className="text-xs text-zinc-500 dark:text-zinc-400 text-center">
          Data points: {data.chartData.length} |
          Latest value: ${data.chartData[data.chartData.length - 1]?.value.toFixed(2)}
        </div>
      </div>
    </div>
  );
}

export default function Home() {
  return (
    <div className="min-h-screen bg-zinc-50 dark:bg-zinc-950 py-8 px-4">
      <div className="container mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-zinc-900 dark:text-zinc-100 mb-2">
            IBKR Portfolio Tracker
          </h1>
          <p className="text-zinc-600 dark:text-zinc-400">
            Real-time performance monitoring for your Interactive Brokers portfolio
          </p>
        </div>

        <IBKRPerformanceDashboard />

        <div className="text-center mt-8">
          <p className="text-xs text-zinc-500 dark:text-zinc-400">
            Data is automatically updated from Vercel KV storage
          </p>
        </div>
      </div>
    </div>
  );
}
