interface IBKRConnectionStatusProps {
  lastUpdated: string;
  isConnected?: boolean;
  onRefresh?: () => void;
  isRefreshing?: boolean;
}

export default function IBKRConnectionStatus({ lastUpdated, isConnected = true, onRefresh, isRefreshing = false }: IBKRConnectionStatusProps) {
  return (
    <div className="bg-white dark:bg-zinc-900 rounded-lg p-4 border border-zinc-200 dark:border-zinc-700 shadow-sm">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-500 animate-pulse' : 'bg-red-500'}`}></div>
            <span className="text-sm font-medium text-zinc-900 dark:text-zinc-100">
              IBKR Connection
            </span>
          </div>
          <span className={`text-xs px-2 py-1 rounded-full ${
            isConnected 
              ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' 
              : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
          }`}>
            {isConnected ? 'Connected' : 'Disconnected'}
          </span>
        </div>
        
        <div className="flex items-center space-x-3">
          <div className="text-right">
            <div className="text-xs text-zinc-500 dark:text-zinc-400">Last Updated</div>
            <div className="text-sm font-medium text-zinc-900 dark:text-zinc-100">
              {new Date(lastUpdated).toLocaleString()}
            </div>
          </div>

          {onRefresh && (
            <button
              onClick={onRefresh}
              disabled={isRefreshing}
              className={`p-2 rounded-lg transition-colors ${
                isRefreshing
                  ? 'text-zinc-400 cursor-not-allowed'
                  : 'text-zinc-500 hover:text-zinc-700 dark:text-zinc-400 dark:hover:text-zinc-200 hover:bg-zinc-100 dark:hover:bg-zinc-800'
              }`}
              title={isRefreshing ? "Refreshing..." : "Refresh data"}
            >
              <svg
                className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            </button>
          )}
        </div>
      </div>
      
      <div className="mt-3 flex items-center justify-between text-xs text-zinc-500 dark:text-zinc-400">
        <span>Data is automatically synced from Interactive Brokers via Vercel KV</span>
        <span className="text-zinc-400 dark:text-zinc-500">Auto-refresh: 30s</span>
      </div>
    </div>
  );
}
