interface PerformanceCardProps {
  label: string;
  value: number;
  isLoading?: boolean;
}

export default function PerformanceCard({ label, value, isLoading }: PerformanceCardProps) {
  const formatPercentage = (val: number) => {
    const formatted = val.toFixed(2);
    const sign = val >= 0 ? '+' : '';
    return `${sign}${formatted}%`;
  };

  const getPerformanceColor = (val: number) => {
    if (val > 0) return 'text-green-600 dark:text-green-400';
    if (val < 0) return 'text-red-600 dark:text-red-400';
    return 'text-zinc-600 dark:text-zinc-400';
  };

  if (isLoading) {
    return (
      <div className="bg-zinc-50 dark:bg-zinc-800 rounded-lg p-4 text-center border border-zinc-200 dark:border-zinc-700 animate-pulse">
        <div className="h-4 bg-zinc-200 dark:bg-zinc-700 rounded w-8 mx-auto mb-2"></div>
        <div className="h-6 bg-zinc-200 dark:bg-zinc-700 rounded w-16 mx-auto"></div>
      </div>
    );
  }

  return (
    <div className="bg-zinc-50 dark:bg-zinc-800 rounded-lg p-4 text-center border border-zinc-200 dark:border-zinc-700 hover:shadow-md transition-all duration-200 hover:scale-105">
      <div className="text-xs font-medium text-zinc-500 dark:text-zinc-400 mb-2 uppercase tracking-wide">
        {label}
      </div>
      <div className={`text-xl font-bold transition-colors duration-200 ${getPerformanceColor(value)}`}>
        {formatPercentage(value)}
      </div>
    </div>
  );
}
