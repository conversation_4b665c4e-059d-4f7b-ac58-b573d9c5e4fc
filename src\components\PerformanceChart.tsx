"use client";
import { useMemo } from 'react';

interface ChartDataPoint {
  date: string;
  value: number;
}

interface PerformanceChartProps {
  data: ChartDataPoint[];
  isLoading?: boolean;
}

export default function PerformanceChart({ data, isLoading }: PerformanceChartProps) {
  const chartStats = useMemo(() => {
    if (!data || data.length === 0) return null;
    
    const values = data.map(d => d.value);
    const min = Math.min(...values);
    const max = Math.max(...values);
    const latest = values[values.length - 1];
    const previous = values[values.length - 2];
    const change = previous ? ((latest - previous) / previous) * 100 : 0;
    
    return { min, max, latest, change };
  }, [data]);

  const generatePath = (points: ChartDataPoint[], width: number, height: number) => {
    if (!points || points.length === 0) return '';
    
    const values = points.map(p => p.value);
    const min = Math.min(...values);
    const max = Math.max(...values);
    const range = max - min || 1;
    
    const pathData = points.map((point, index) => {
      const x = (index / (points.length - 1)) * width;
      const y = height - ((point.value - min) / range) * height;
      return `${index === 0 ? 'M' : 'L'} ${x} ${y}`;
    }).join(' ');
    
    return pathData;
  };

  if (isLoading) {
    return (
      <div className="bg-white dark:bg-zinc-900 rounded-lg p-6 border border-zinc-200 dark:border-zinc-700 animate-pulse">
        <div className="h-6 bg-zinc-200 dark:bg-zinc-700 rounded w-32 mb-4"></div>
        <div className="h-48 bg-zinc-200 dark:bg-zinc-700 rounded"></div>
      </div>
    );
  }

  if (!data || data.length === 0) {
    return (
      <div className="bg-white dark:bg-zinc-900 rounded-lg p-6 border border-zinc-200 dark:border-zinc-700">
        <h3 className="text-lg font-semibold text-zinc-900 dark:text-zinc-100 mb-4">
          Portfolio Value Chart
        </h3>
        <div className="h-48 flex items-center justify-center text-zinc-500 dark:text-zinc-400">
          No chart data available
        </div>
      </div>
    );
  }

  const svgWidth = 800;
  const svgHeight = 200;
  const path = generatePath(data, svgWidth, svgHeight);

  return (
    <div className="bg-white dark:bg-zinc-900 rounded-lg p-6 border border-zinc-200 dark:border-zinc-700 shadow-sm">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-zinc-900 dark:text-zinc-100">
          Portfolio Value Chart
        </h3>
        {chartStats && (
          <div className="text-right">
            <div className="text-2xl font-bold text-zinc-900 dark:text-zinc-100">
              ${chartStats.latest.toFixed(2)}
            </div>
            <div className={`text-sm ${chartStats.change >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
              {chartStats.change >= 0 ? '+' : ''}{chartStats.change.toFixed(2)}%
            </div>
          </div>
        )}
      </div>
      
      <div className="relative h-48 overflow-hidden rounded-lg bg-zinc-50 dark:bg-zinc-800">
        <svg
          width="100%"
          height="100%"
          viewBox={`0 0 ${svgWidth} ${svgHeight}`}
          className="absolute inset-0"
          preserveAspectRatio="none"
        >
          <defs>
            <linearGradient id="chartGradient" x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" stopColor="rgb(59, 130, 246)" stopOpacity="0.4" />
              <stop offset="50%" stopColor="rgb(147, 51, 234)" stopOpacity="0.2" />
              <stop offset="100%" stopColor="rgb(59, 130, 246)" stopOpacity="0.05" />
            </linearGradient>
            <filter id="glow">
              <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
              <feMerge>
                <feMergeNode in="coloredBlur"/>
                <feMergeNode in="SourceGraphic"/>
              </feMerge>
            </filter>
          </defs>
          
          {/* Fill area under the curve */}
          <path
            d={`${path} L ${svgWidth} ${svgHeight} L 0 ${svgHeight} Z`}
            fill="url(#chartGradient)"
            className="transition-all duration-1000 ease-out"
          />
          
          {/* Main line */}
          <path
            d={path}
            fill="none"
            stroke="url(#lineGradient)"
            strokeWidth="3"
            className="transition-all duration-1000 ease-out"
            filter="url(#glow)"
          />

          {/* Additional gradient for the line */}
          <defs>
            <linearGradient id="lineGradient" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" stopColor="rgb(59, 130, 246)" />
              <stop offset="50%" stopColor="rgb(147, 51, 234)" />
              <stop offset="100%" stopColor="rgb(59, 130, 246)" />
            </linearGradient>
          </defs>
        </svg>
        
        {/* Animated dots for data points */}
        <div className="absolute inset-0">
          {data.slice(-5).map((point, index) => {
            const values = data.map(p => p.value);
            const min = Math.min(...values);
            const max = Math.max(...values);
            const range = max - min || 1;
            
            const x = ((data.indexOf(point)) / (data.length - 1)) * 100;
            const y = 100 - ((point.value - min) / range) * 100;
            
            return (
              <div
                key={`${point.date}-${index}`}
                className="absolute w-2 h-2 bg-blue-500 rounded-full animate-pulse"
                style={{
                  left: `${x}%`,
                  top: `${y}%`,
                  transform: 'translate(-50%, -50%)',
                  animationDelay: `${index * 200}ms`
                }}
              />
            );
          })}
        </div>
      </div>
      
      {chartStats && (
        <div className="mt-4 grid grid-cols-3 gap-4 text-center">
          <div>
            <div className="text-xs text-zinc-500 dark:text-zinc-400">Min</div>
            <div className="font-semibold text-zinc-900 dark:text-zinc-100">
              ${chartStats.min.toFixed(2)}
            </div>
          </div>
          <div>
            <div className="text-xs text-zinc-500 dark:text-zinc-400">Max</div>
            <div className="font-semibold text-zinc-900 dark:text-zinc-100">
              ${chartStats.max.toFixed(2)}
            </div>
          </div>
          <div>
            <div className="text-xs text-zinc-500 dark:text-zinc-400">Data Points</div>
            <div className="font-semibold text-zinc-900 dark:text-zinc-100">
              {data.length}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
