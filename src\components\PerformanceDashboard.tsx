"use client";
import { useEffect, useState } from "react";
import PerformanceCard from "./PerformanceCard";
import PerformanceChart from "./PerformanceChart";
import IBKRConnectionStatus from "./IBKRConnectionStatus";

type Summary = {
  d1: number;
  d7: number;
  mtd: number;
  m1: number;
  ytd: number;
  y1: number;
};

type PerformanceData = {
  lastUpdated: string;
  summary: Summary;
  chartData: { date: string; value: number }[];
};

const PERIODS = [
  { key: "d1", label: "1D" },
  { key: "d7", label: "7D" },
  { key: "mtd", label: "MTD" },
  { key: "m1", label: "1M" },
  { key: "ytd", label: "YTD" },
  { key: "y1", label: "1Y" },
];

export default function PerformanceDashboard() {
  const [data, setData] = useState<PerformanceData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());

  const fetchData = async () => {
    try {
      const response = await fetch("/api/ibkr-performance");
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const result = await response.json();
      setData(result);
      setError(null);
      setLastRefresh(new Date());
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch data');
      console.error('Error fetching performance data:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();

    // Set up auto-refresh every 30 seconds
    const interval = setInterval(fetchData, 30000);

    return () => clearInterval(interval);
  }, []);

  const handleManualRefresh = () => {
    setLoading(true);
    fetchData();
  };

  if (loading) {
    return (
      <div className="w-full max-w-6xl mx-auto space-y-6">
        {/* Connection Status Loading */}
        <div className="bg-white dark:bg-zinc-900 rounded-lg p-4 border border-zinc-200 dark:border-zinc-700 animate-pulse">
          <div className="h-4 bg-zinc-200 dark:bg-zinc-700 rounded w-48"></div>
        </div>
        
        {/* Performance Cards Loading */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          {Array.from({ length: 6 }).map((_, i) => (
            <PerformanceCard key={i} label="" value={0} isLoading={true} />
          ))}
        </div>
        
        {/* Chart Loading */}
        <PerformanceChart data={[]} isLoading={true} />
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full max-w-6xl mx-auto">
        <div className="bg-white dark:bg-zinc-900 rounded-lg shadow-lg p-8 border border-red-200 dark:border-red-800">
          <div className="text-center">
            <div className="text-red-600 dark:text-red-400 mb-4 text-4xl">⚠️</div>
            <div className="text-xl font-semibold text-red-600 dark:text-red-400 mb-2">
              Error Loading Data
            </div>
            <div className="text-sm text-zinc-600 dark:text-zinc-400 mb-4">{error}</div>
            <button 
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="w-full max-w-6xl mx-auto">
        <div className="bg-white dark:bg-zinc-900 rounded-lg shadow-lg p-8 border border-zinc-200 dark:border-zinc-700">
          <div className="text-center text-zinc-600 dark:text-zinc-400">
            No performance data available
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-6xl mx-auto space-y-6 animate-fade-in">
      {/* IBKR Connection Status */}
      <IBKRConnectionStatus
        lastUpdated={data.lastUpdated}
        onRefresh={handleManualRefresh}
        isRefreshing={loading}
      />
      
      {/* Performance Cards */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
        {PERIODS.map((period, index) => {
          const value = data.summary[period.key as keyof Summary];
          return (
            <div 
              key={period.key}
              className="animate-slide-up"
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <PerformanceCard 
                label={period.label} 
                value={value} 
              />
            </div>
          );
        })}
      </div>
      
      {/* Performance Chart */}
      <div className="animate-slide-up" style={{ animationDelay: '600ms' }}>
        <PerformanceChart data={data.chartData} />
      </div>
    </div>
  );
}
